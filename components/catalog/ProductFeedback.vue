<template>
	<!-- comments -->
	<div class="feedback" :class="{'active': active}">
		<div class="header" @click="active = !active">
			<BaseCmsLabel code='tab_comments' tag="div" class="title" />
		</div>
		<div class="body">
			<div class="col col1">
				<div class="slider">
					<BaseFeedbackComments :per-page="5" :items="item.feedback_comment_widget.items" v-slot="{items}">
						<div v-if="items.length" class="comments">
							<UiSwiper :options="{slidesPerView: 2.4, slidesPerGroup: 2, spaceBetween: 15}">
								<BaseUiSwiperSlide v-for="item in items" :key="item.id">
									<div class="comment-item">
										<!-- FIXME naslov komentara -->
										<BaseFeedbackRatesWidget :rate="item.rate" v-slot="{stars, rate}">
											<div class="comment-rate">
												<FeedbackStars :stars="stars" class="small" />
												<span class="value">{{Math.round(rate)}}</span>
											</div>
										</BaseFeedbackRatesWidget>
										<div class="comment-message">{{ item.message }}</div>
										<div class="comment-author" v-if="item.display_name">{{ item.display_name }}</div>
										
										<!--
										FIXME podatak da li je komentar objavljen od strane kupca koji je kupio proizvod
										<span class="comment-purchase-confirmed"><BaseCmsLabel code='comment_purchase_confirmed' /></span>
										-->
									</div>
								</BaseUiSwiperSlide>
							</UiSwiper>
						</div>
						<div v-else class="no-comments"><BaseCmsLabel code="no_comments" /></div>
					</BaseFeedbackComments>
				</div>
			</div>
			<div class="col col2">
				<div class="col2-cnt">
					<BaseFeedbackRatesWidget :data="item.feedback_rate_widget" v-slot="{stars, rate}">
						<div class="average-rate">
							<div v-if="rate > 0" class="average-rate-value">{{ rate }}</div>
							<FeedbackStars :stars="stars" class="small" />
							<div><BaseCmsLabel code="average_rate" /></div>
						</div>
						<div class="rate-votes"><span>{{ item.feedback_rate_widget.rates_votes }}</span> <BaseCmsLabel code="review" /></div>
					</BaseFeedbackRatesWidget>
					<div class="btn btn-outline btn-all-reviews" @click="showComments = true">
						<BaseCmsLabel code='show_all_reviews' />
					</div>
				</div>
			</div>
		</div>
	</div>
	<Teleport to="body">
		<LazyFeedbackComments :item="item" v-if="showComments" @close="showComments = false" />
	</Teleport>
</template>

<script setup>
	const props = defineProps({
		item: Object,
	});
	const active = ref(true);
	const showComments = ref(false);
</script>

<style scoped lang="less">
	.feedback{position: relative; overflow: hidden; background: #fff; border-radius: 12px; min-height: 66px;}
	.active{
		.body{display: flex;}
		.header:after{.rotate(180deg);}
	}
	.body{display: flex; display: none;}
	.header{
		position: absolute; top: 0; left: 0; right: 0; padding: 15px 24px; z-index: 10; display: flex; justify-content: space-between; align-items: center; cursor: pointer;
		&:after{.icon-arrow-down(); font: 12px/1 var(--fonti);}
	}
	.col1{flex-grow: 1; padding: 65px 15px 15px; background: #F5F5F5;}
	.slider{width: 585px;}

	.col2{flex: 0 0 325px; background: #fff; display: flex; align-items: center; padding: 15px 15px 80px; font-size: 15px; position: relative;}
	.title{font-size: 24px; font-weight: bold;}
	.average-rate-value{font-size: 36px; font-weight: bold;}
	.col2-cnt{display: flex; flex-direction: column; gap: 35px;}
	.rate-votes{
		line-height: 1.4; text-transform: capitalize;
		span{font-size: 36px; font-weight: bold; display: block;}
	}
	:deep(.stars){
		margin: 8px 0 10px; gap: 5px;
	}
	.btn-all-reviews{position: absolute; bottom: 15px; left: 15px; right: 15px;}


	.comment-item{background: #fff; border-radius: 12px; padding: 15px; flex-grow: 0; flex-shrink: 0; font-size: 14px; color: var(--gray5); height: 100%; display: flex; flex-direction: column;}
	.comment-message{flex-grow: 1;}
	.comment-author{padding-top: 15px;}
	.no-comments{padding: 0 0 0 10px;}
	:deep(.swiper-scrollbar){height: 4px;
		.swiper-scrollbar-drag{height: 4px;}
	}
	:deep(.swiper-slide){height: auto;}
	:deep(.swiper-button){
		left: -10px; width: 33px; height: 33px; opacity: 1; background: var(--gray5);
		&:before{font-size: 10px; color: #fff; margin-left: 2px;}
	}
	:deep(.swiper-button-next){left: auto; right: -15px;}
	:deep(.swiper-button-disabled){display: none;}
	.comment-rate{
		display: flex; align-items: center; align-items: center; gap: 8px; font-weight: bold; color: var(--textColor); font-size: 12px; line-height: 1; padding: 0 0 10px;
		.stars{gap: 0; margin: 0 0 5px;}
	}
</style>