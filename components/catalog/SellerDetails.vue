<template>
	<div class="modal">
		<div class="body">
			<div class="wrapper posr">
				<span class="close" @click="$emit('close')">X</span>
				<div class="content">
					<div class="section" v-if="item.content">
						<div class="section-title"><BaseCmsLabel code="seller_tab_information" /></div>
						<div class="cms-content" v-html="item.content" />
					</div>
					
					<div class="section" v-if="item?.return_company_title || item?.return_address || item?.customer_email || item?.customer_phone || item?.return_courier_service || item?.return_costs_covered">
						<div class="section-title"><BaseCmsLabel code="seller_return_policy_title" /></div>
						<div class="content-table">
							<div v-if="item?.return_company_title" class="table-row name">
								<span class="title"><BaseCmsLabel code="seller_return_company_name" /></span>
								<span class="value">{{ item.return_company_title }}</span>
							</div>
							<div v-if="item?.return_address" class="table-row address">
								<span class="title"><BaseCmsLabel code="seller_return_address" /></span>
								<span class="value">{{ item.return_address }}</span>
							</div>
							<div v-if="item?.customer_email" class="table-row email">
								<span class="title"><BaseCmsLabel code="seller_return_email" /></span>
								<span class="value">{{ item.customer_email }}</span>
							</div>
							<div v-if="item?.customer_phone" class="table-row phone">
								<span class="title"><BaseCmsLabel code="seller_return_phone" /></span>
								<span class="value">{{ item.customer_phone }}</span>
							</div>
							<div v-if="courierName?.length" class="table-row courier">
								<span class="title"><BaseCmsLabel code="seller_return_courier_service" /></span>
								<span class="value">
									<template v-for="(name, index) in courierName" :key="index"><BaseCmsLabel :code="'courier_' + name" /><span v-if="index < courierName.length - 1">, </span> </template>
								</span>
							</div>
							<div v-if="costsCovered?.length" class="table-row cost">
								<span class="title"><BaseCmsLabel code="seller_return_cost" /></span>
								<span class="value">
									<template v-for="(item, index) in costsCovered" :key="index"><BaseCmsLabel :code="'costs_covered_' + item" /><span v-if="index < costsCovered.length - 1">, </span> </template>
								</span>
							</div>
						</div>
					</div>

					<div class="terms" v-if="item.content || item.element_return_policy || item.element_gdpr_compliance || item.element_other_terms_conditions">
						<div class="section" v-if="item.element_return_policy">
							<div class="seller-detail-content-desc cms-content" v-html="item.element_return_policy"></div>
						</div>

						<div class="section" v-if="item.element_gdpr_compliance">
							<div class="section-title"><BaseCmsLabel code="seller_gdpr_compliance_title" /></div>
							<div class="seller-detail-content-desc cms-content" v-html="item.element_gdpr_compliance"></div>
						</div>

						<div class="section" v-if="item.element_other_terms_conditions">
							<div class="section-title"><BaseCmsLabel code="seller_terms_conditions_title" /></div>
							<div class="seller-detail-content-desc cms-content" v-html="item.element_other_terms_conditions"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="backdrop" />
	</div>
</template>

<script setup>
	const props = defineProps(['item']);

	const courierName = computed(() => {
		if (props.item?.return_courier_service) {
			// Split the return_courier_service value into an array of words
			const name = props.item.return_courier_service.split(', ')?.map(word => word.toLowerCase());

			// Filter out any empty words
			const filteredName = name.filter(word => word.trim() !== '');

			return filteredName;
		}

		return [];
	});

	const costsCovered = computed(() => {
		if (props.item?.return_costs_covered) {
			// Split the return_courier_service value into an array of words
			const name = props.item.return_costs_covered.split(', ')?.map(word => word.toLowerCase());

			// Filter out any empty words
			const filteredName = name.filter(word => word.trim() !== '');

			return filteredName;
		}

		return [];
	});	
</script>

<style scoped lang="less">
	.modal{position: fixed; bottom: 0; left: 0; right: 0; z-index: 0; z-index: 9999;}
	.body{background: #fff; padding: 25px; position: relative; z-index: 10;}
	.close{
		position: absolute; top: 0px; right: 25px; background: #fff; font-size: 20px; cursor: pointer; display: flex; z-index: 10; align-items: center; justify-content: center; font-size: 0; width: 30px; height: 30px;
		&:before{.icon-x(); font: 20px/1 var(--fonti);}
	}
	.section{padding-bottom: 35px;}
	.content{
		max-height: 67dvh; overflow: auto; overscroll-behavior: contain; padding-right: 20px;
		&::-webkit-scrollbar{width: 5px;}
		&::-webkit-scrollbar-thumb{background: var(--blue); border-radius: 100px;}
		&::-webkit-scrollbar-track{background: var(--gray2); border-radius: 100px;}
	}
	.section-title{font-size: 18px; font-weight: bold; padding: 0 0 15px;}
	.terms{column-count: 2; column-gap: 40px;}
	.content-table{max-width: calc(50% - 40px); font-size: 15px;}
	.table-row{
		padding: 7px 10px; display: flex; gap: 25px;
		&:nth-child(odd){background: var(--gray3);}
		.title{font-weight: bold;}
		.value{margin-left: auto; width: 45%; text-align: right;}
	}
</style>