<template>
	<div class="seller">
		<div class="col col1" v-if="item?.main_image">
			<BaseUiImage loading="lazy" :data="item.main_image_thumbs?.['width200-height200']" default="/images/no-image.jpg" />
		</div>
		<div class="col col2">
			<div class="title">{{ item.title }}</div>
			<div class="closed strong" v-if="item?.status == 'Close' && (item.closed_from || item.closed_to)">
				<BaseCmsLabel code="seller_closed" />
				(
				<template v-if="item?.closed_from">
					<BaseUtilsFormatDate :date="item.closed_from" />
				</template>
				<template v-if="item?.closed_to"> - <BaseUtilsFormatDate :date="item.closed_to" /> </template>
				<template v-else> - </template>
				)
			</div>
			<div class="details">
				<template v-if="item?.corporate_name">{{item.corporate_name}}, </template>
				<template v-if="item?.address">{{item.address}}, </template>
				<template v-if="item?.zipcode">{{item.zipcode}}, </template>
				<template v-if="item?.city">{{item.city}}, </template>
				<template v-if="item?.country">{{item.country}}, </template>
				<span v-if="item?.identification_number">
					<strong><BaseCmsLabel code="seller_identification_number" />: </strong> {{item.identification_number}},
				</span>
				<span v-if="item?.vat_number"><strong><BaseCmsLabel code="seller_vat_number" />: </strong> {{item.vat_number}}</span>
			</div>
			<div class="country" v-if="item?.shipping_country">
				<BaseCmsLabel code="seller_shipping_country" /> <strong>{{item.shipping_country}}</strong>
			</div>
		</div>
		<div class="col col3">
			<div class="links">
				<NuxtLink v-if="item.url_product_page && !props.hideProductsUrl" class="link" :to="item.url_product_page_without_domain">
					<BaseCmsLabel code="tab_seller_items" />
				</NuxtLink>
				<span v-if="item.content || item.element_return_policy || item.element_gdpr_compliance || item.element_other_terms_conditions" class="link" @click="activeDetails = true"><BaseCmsLabel code="tab_seller_information" /></span>
			</div>
		</div>
	</div>
	<Teleport to="body">
		<LazyCatalogSellerDetails v-if="activeDetails" :item="props.item" :courierName="props.courierName" :costsCovered="props.costsCovered" @close="activeDetails = false" />
	</Teleport>	
</template>

<script setup>
	const props = defineProps({
		item: Object,
		hideProductsUrl: {
			type: Boolean,
			default: false,
		},
	});
	const activeDetails = ref(false);
</script>

<style lang="less" scoped>
	.seller{background: #fff; border-radius: 12px; display: flex; padding: 25px; gap: 25px; font-size: 12px; margin-bottom: 25px; color: var(--gray5);}
	.col1{max-width: 100px;}
	.col2{flex-grow: 1;
		&>div:last-child{padding: 0;}
	}
	.col3{align-self: flex-end;}
	.title{font-size: 18px; font-weight: bold; padding: 0 0 15px; color: var(--textColor);}
	.details{padding: 0 0 15px;}
	.links{display: flex; gap: 25px;}
	.link{color: var(--blueDark);}
</style>
