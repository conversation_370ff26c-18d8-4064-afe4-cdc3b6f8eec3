<template>
	<BaseCatalogFilters v-slot="{searchFields}" v-model="filterData">
		<template v-if="categoriesFilter">
			<div class="filters">
				<BaseCatalogFilterItem :item="categoriesFilter" v-slot="{fields, onClear, onFilter, selectedFilters}">
					<div class="clear" :class="{'active': !selectedFilters.length}" @click="onClear">Prikaži sve</div>
					<template v-for="searchField in fields" :key="searchField.id">
						<div v-if="searchField.level === '2'" class="filter">
							<input type="checkbox" :name="searchField.filter_url" :id="searchField.unique_code" :value="searchField.filter_url" :checked="searchField.selected" @change="onFilter" />
							<label :for="searchField.unique_code">{{ searchField.title }}</label>
						</div>
					</template>
				</BaseCatalogFilterItem>
			</div>
		</template>
	</BaseCatalogFilters>
</template> 

<script setup>
	const filterData = ref();
	const categoriesFilter = computed(() => {
		const filter = filterData.value.searchFields.find(item => item.code === 'category');
		if(filter) return filter;
		return null;
	});
</script>

<style lang="less" scoped>
	.filters{display: flex; gap: 10px;}
	.filter, .clear{
		border: 1px solid var(--blueDark); border-radius: 8px; color: var(--blueDark); cursor: pointer; overflow: hidden; font-size: 15px; .transition(all); line-height: 1.2;
		label{
			padding: 11px 20px;
			&:before{display: none;}
		}
		&:hover, input:checked + label, &.active{background: var(--blueDark); color: var(--white);}
	}
	.clear{padding: 11px 20px;}
</style>
