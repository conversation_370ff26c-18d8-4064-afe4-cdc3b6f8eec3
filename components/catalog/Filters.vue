<template>
	<Body :class="{'o-active': mFilters}" />
	<BaseCatalogFilters v-slot="{searchFields, productsCounter, loading}" v-model="filterData">
		<BaseCatalogActiveFilters v-slot="{items: activeFilters, onRemove}">
			<div v-if="searchFields?.length" class="cf" :class="{'active': mFilters}">
				<!-- mobile filters header -->
				<div class="cf-header-m">
					<div class="cf-header-search-cnt">
						<input v-model="searchQuery" :placeholder="labels.get('search_filters')" class="cf-header-search-input" type="text" />
					</div>
					<div class="cf-header-close" @click="closeFilters()"><BaseCmsLabel code='close' /></div>
				</div>
				<div class="cf-items">
					<!-- active filters -->
					<div v-if="activeFilters.length" class="cf-active-items">
						<div class="cf-active-title"><span class="title"><BaseCmsLabel code="active_filters" /></span><span class="remove" @click="onRemove()"><BaseCmsLabel code="remove" /></span></div>
						<div class="cf-active-items-wrapper">
							<div v-for="filter in activeFilters" :key="filter.id" class="cf-active-item" @click="onRemove(filter)">
								<span class="icon">
									<BaseFeedbackRatesWidget v-if="filter.field == 'rates'" :data="{rates_status: 2, rates: filter.slug}" v-slot="{stars}">
										<span class="cf-rate rates-container" v-html="stars" />
									</BaseFeedbackRatesWidget>
									{{ filter.title }}
								</span>
							</div>
						</div>
					</div>

					<span class="m-toolbar"></span>
					<!-- items -->
					<BaseCatalogFilterItem v-for="filter in filteredFields" :key="filter.id" :item="filter" :group="filter.code == 'manufacturer' ? 'alphabet' : null" v-slot="{fields, onSearch, selectedFilters, onClear, active, onFilter, onToggle}" :active="false">
						<div class="cf-item" :class="['cf-item-' + filter.code, filter.layout ? 'cf-layout-' + filter.layout : '', {'active': active}]">
							<div class="cf-item-title" @click="onToggle($event, {all: mobileBreakpoint ? false : true})"><span>{{ filter.label }}<span v-if="selectedFilters.length" class="counter">{{ selectedFilters.length }}</span></span></div>
							<div v-if="active" class="cf-item-dropdown">
								<div class="cf-search-cnt" v-if="filter.code == 'manufacturer'">
									<input :placeholder="labels.get('search_filters')" class="cf-search-input" type="text" @keyup="onSearch" />
								</div>
								<div class="cf-row-container">
									<template v-if="filter.layout == 'sf'">
										<BaseCatalogFilterFieldSlider :item="filter" />
									</template>
									<template v-else>
										<template v-if="filter.code == 'manufacturer'">
											<!-- FIXME kod bradova treba dodati podatak o izdvojenim brandovima -->
											<div class="cf-row-group" v-for="alphabet in fields" :key="alphabet">
												<template v-if="alphabet?.items?.length">
													<div class="cf-group-title">{{alphabet.alphabet}}</div>
													<div v-for="searchField in alphabet.items" :key="searchField.id" class="cf-row" :class="[searchField.level && 'cf-row-level' + searchField.level, {'cf-row-not-available': searchField.total_available < 1}]">
														<input type="checkbox" :name="filter.filter_url" :id="searchField.unique_code" :value="searchField.filter_url" :checked="searchField.selected" @change="onFilter" />
														<label :for="searchField.unique_code">
															<span v-html="searchField.title" />
															<span v-show="searchField.total_available > 0" class="cf-counter">{{ searchField.total_available }}</span>
														</label>
													</div>
												</template>
											</div>
										</template>
										<template v-else>
											<div v-for="searchField in fields" :key="searchField.id" class="cf-row" :class="[searchField.level && 'cf-row-level' + searchField.level, {'cf-row-not-available': searchField.total_available < 1}]">
												<input type="checkbox" :name="filter.filter_url" :id="searchField.unique_code" :value="searchField.filter_url" :checked="searchField.selected" @change="onFilter" />
												<label :for="searchField.unique_code">
													<div class="cf-color-img" v-if="filter.code == 'a_barva-pim-6698538' && searchField.image_upload_path">
														<BaseUiImage :src="searchField.image_upload_path" width="20" height="20" alt="" default="/images/no-image-50.jpg" loading="lazy" />
													</div>
													<BaseFeedbackRatesWidget v-if="filter.code == 'rates'" :data="{rates_status: 2, rates: searchField.filter_url}" v-slot="{stars}">
														<span class="cf-rate rates-container" v-html="stars" />
													</BaseFeedbackRatesWidget>
													<span v-html="searchField.title" />
													<span v-show="searchField.total_available > 0" class="cf-counter">{{ searchField.total_available }}</span>
												</label>
											</div>
										</template>
									</template>
								</div>
								<div v-if="selectedFilters.length" class="cf-item-clear" @click="onClear"><span><BaseCmsLabel code="clear_filtering_btn" /></span></div>
							</div>
						</div>
					</BaseCatalogFilterItem>

					<!-- buttons -->
					<div class="cf-btns">
						<div v-if="activeFilters?.length" @click="onRemove()" class="cf-clear"><span><BaseCmsLabel code="clear_filtering_btn" /></span></div>
						<div class="cf-btn-confirm btn" @click="closeFilters()">
							<UiLoader class="inline" v-if="loading" />
							<span>{{labels.get('filters_confirm').replace('%v%', productsCounter)}}</span>
						</div>
					</div>
				</div>
			</div>
			<!-- mobile button -->
			<div v-if="searchFields?.length || activeFilters?.length" @click="openFilters()" class="cf-btn-m btn"><BaseCmsLabel code="filter_btn_m" tag="span" /></div>
		</BaseCatalogActiveFilters>
	</BaseCatalogFilters>
</template>

<script setup>
	const {onClickOutside} = useDom();
	const {emit, subscribe} = useEventBus();
	const labels = useLabels();
	const {mobileBreakpoint} = inject('rwd');

	//mobile toggle filters
	let mFilters = ref(false);
	function openFilters() {
		mFilters.value = true;
	}
	function closeFilters() {
		mFilters.value = false;
	}

	// search and filter filters (mobile only)
	const filterData = ref();
	const searchQuery = ref('');
	const filteredFields = computed(() => {
		if(searchQuery.value) {
			return filterData.value.searchFields.filter(field =>
				field.label.toLowerCase().includes(searchQuery.value.toLowerCase())
			);
		}
		return filterData.value.searchFields;
	});

	onClickOutside('.cf-item', () => {
		if(!mobileBreakpoint.value) emit('catalogFilterItemToggle');
	});
</script>

<style lang="less" scoped>
	@keyframes showElement{
		to{opacity: 1;}
	}
	.m-toolbar{
		display: none;

		@media (max-width: @m){display: block;}
	}

	.cf{
		margin-bottom: 30px;

		@media (max-width: @m){
			margin: 0; background: var(--white); border-radius: 20px 20px 0 0; position: fixed; left: 0; right: 0; bottom: 0; top: calc(~"100% - -200px"); z-index: 1111; .transition(top);
			&:before{.pseudo(auto, 54px); background: rgba(0, 0, 0, 0.40); top: -54px; left: 0; right: 0; z-index: 1; opacity: 0;}
			&.active{
				top: 54px;
				&:before{animation: showElement 0.3s forwards; animation-delay: 0.3s;}
				.cf-btns{display: flex;}
			}
			:deep(.c-toolbar-extra){display: block;}
		}
	}

	.cf-header-m{
		display: none;

		@media (max-width: @m){display: flex; align-items: center; justify-content: flex-end; height: 66px; padding: 5px 12px; background: var(--white); border-bottom: 1px solid var(--gray3); border-radius: 20px 20px 0 0; z-index: 1;}
	}
	.cf-header-search-cnt{
		flex-grow: 1; margin-right: 12px; position: relative;
		&:before{.icon-search(); font: 18px/1 var(--fonti); color: var(--black); position: absolute; top: 12px; right: 12px;}
	}
	.cf-header-search-input{height: 42px; padding: 0 46px 0 16px; background: var(--gray3); border: none; border-radius: 8px; font-size: 16px;}

	.cf-active-items{
		display: none; padding: 0 12px 24px; border-bottom: 1px solid var(--gray3);

		@media (max-width: @m){display: block;}
	}
	.cf-active-title{
		display: flex; align-items: center; justify-content: space-between; height: 53px; color: var(--black);
		.title{flex-grow: 1; font-size: 16px; font-weight: 600;}
		.remove{font-size: 14px; font-weight: 500; letter-spacing: -0.28px;}
	}
	.cf-active-items-wrapper{display: flex; flex-wrap: wrap; gap: 8px;}
	.cf-active-item{
		display: flex; align-items: center; justify-content: center; height: 41px; padding: 5px 12px; background: var(--gray3); border-radius: 8px; font-size: 14px; color: var(--black);
		.icon{
			display: flex; align-items: center; padding-right: 28px; position: relative;
			&:before{.pseudo(16px,16px); display: flex; align-items: center; justify-content: center; background: var(--blueDark); border-radius: 100%; .icon-plus(); font: 9px/1 var(--fonti); color: var(--white); .rotate(45deg); position: absolute; right: 0;}

		}
	}
	
	.cf-items{
		display: flex; flex-wrap: wrap; flex-grow: 1; gap: 12px;

		@media (max-width: @m){
			display: block; height: calc(~"100% - 152px"); gap: unset; overflow: hidden; overflow-y: auto;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.cf-item{
		position: relative;
		&.active, &.selected {
			.cf-item-title{
				color: var(--black);
				span:before{color: var(--black);}
			}
		}
		&.active .cf-item-title span:before{.rotate(180deg);}

		@media (max-width: @m){
			border-bottom: 1px solid var(--gray3);
			&.active .cf-item-title:before{.rotate(180deg);}
		}
	}
	.cf-item-title{
		display: flex; align-items: center; justify-content: center; height: 42px; padding: 5px 16px; background: var(--white); border-radius: 8px; font-size: 15px; font-weight: 500; letter-spacing: -0.28px; color: #6D6D6D; white-space: nowrap; .transition(color); cursor: pointer;
		&>span{
			padding-right: 26px; position: relative; display: flex; align-items: center;
			&:before{.icon-arrow-fill(); font: 11px/1 var(--fonti); color: #6D6D6D; position: absolute; top: 5px; right: 0; .transition(all);}
		}
		.counter{display: inline-flex; align-items: center; justify-content: center; background: var(--black); height: 14px; margin-left: 6px; padding: 2px 7px; border-radius: 54px; font-size: 10px; font-weight: 500; letter-spacing: -0.2px; color: var(--white);}

		@media (max-width: @m){
			justify-content: unset; height: unset; min-height: 53px; padding: 12px 55px 12px 12px; border-radius: 0; font-size: 16px; font-weight: 600; letter-spacing: unset; color: var(--black); white-space: unset; position: relative;
			&:before{.icon-arrow-down(); font: 10px/1 var(--fonti); font-weight: normal; color: var(--black); position: absolute; right: 18px; .transition(all);}
			&>span{
				padding-right: 0; position: relative;
				&:before{content: none;}
			}
			.counter{display: inline-flex; align-items: center; justify-content: center; background: var(--black); height: 14px; margin-left: 6px; padding: 2px 7px; border-radius: 54px; font-size: 10px; font-weight: 500; letter-spacing: -0.2px; color: var(--white);}

		}
	}
	.cf-item-dropdown{
		display: block; width: 375px; padding: 0 12px 0 24px; background: var(--white); border-radius: 8px; position: absolute; top: calc(~"100% - -4px"); z-index: 111; box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12);
		
		@media (max-width: @m){width: 100%; padding: 0 12px; border-radius: 0; position: static; box-shadow: none; z-index: 0;}
	}

	.cf-layout-sf .cf-row-container{overflow: initial;}
	.cf-search-cnt{
		margin-top: 24px; margin-right: 12px; position: relative;
		&:before{.icon-search(); font: 21px/1 var(--fonti); color: var(--black); position: absolute; top: 14px; left: 12px;}

		@media (max-width: @m){
			margin: 0 0 12px;
			&:before{font-size: 18px; top: 12px; left: unset; right: 12px;}
		}
	}
	.cf-search-input{
		height: 50px; padding-left: 44px; background: var(--gray3); border: none; border-radius: 8px; font-size: 16px;
		
		@media (max-width: @m){height: 42px; padding: 5px 16px;}
	}

	.cf-row-container{
		display: flex; flex-direction: column; max-height: 400px; gap: 12px; padding: 24px 0 24px; border-right: 10px solid var(--white); overflow: hidden; overflow-y: auto;
		&::-webkit-scrollbar{width: 4px; height: 4px; background: var(--gray2); border-radius: 6px;}
		&::-webkit-scrollbar-thumb{background: var(--blueDark); border-radius: 6px; border: 4px solid transparent;}
		&::-webkit-scrollbar-track {border-top: 24px solid var(--white); border-bottom: 24px solid var(--white); border-radius: 6px;}
		&::-webkit-scrollbar-track-piece {margin-top: 24px; margin-bottom: 24px; border-radius: 6px;}
		
		@media (max-width: @m){
			max-height: 380px; gap: 14px; padding: 0 0 24px; border-right: unset;
			&::-webkit-scrollbar{width: 3px; height: 3px;}
			&::-webkit-scrollbar-thumb{background: var(--blueDark); border: 3px solid transparent;}
			&::-webkit-scrollbar-track {border-top: unset; border-bottom: 24px solid var(--white);}
			&::-webkit-scrollbar-track-piece {margin-top: 0; margin-bottom: 24px;}
		}
	}
	.cf-row{
		label{font-size: 16px; line-height: 1.3; letter-spacing: -0.32px; display: flex; align-items: center;}

		@media (max-width: @m){label{font-size: 14px; letter-spacing: 0;}}
	}
	.cf-row-group{
		display: flex; gap: 10px; flex-direction: column; border-bottom: 1px solid var(--gray2); padding-bottom: 10px; margin-bottom: 10px; margin-right: 10px;
		&:last-child{border: 0; margin-bottom: 0; padding-bottom: 0;}
	}
	.cf-group-title{font-size: 18px;}
	.cf-counter{display: flex; align-items: center; justify-content: center; background: #E3E3E4; height: 17px; margin-left: 6px; padding: 2px 10px; border-radius: 54px; font-size: 12px; font-weight: 500; letter-spacing: -0.24px; color: var(--black);}

	.cf-item-clear{
		display: flex; align-items: center; justify-content: center; padding: 22px 16px; border-top: 1px solid var(--gray2); cursor: pointer;
		span{
			padding-left: 24px; position: relative;
			&:before{.icon-refresh(); font: 18px/1 var(--fonti); color: var(--black); position: absolute; top: 2px; left: 0; .transition(color);}
		}

		@media (max-width: @m){
			padding: 12px; font-size: 14px;
			span{
				padding-left: 22px;
				&:before{font-size: 16px;}
			}
		}
	}
	.cf-btns{
		@media (max-width: @m){display: none; align-items: center; justify-content: center; padding: 19px 12px; background: var(--white); position: fixed; bottom: 0; left: 0; right: 0; box-shadow: 0px -4px 10px 0px rgba(0, 0, 0, 0.18); z-index: 11;}
	}
	.cf-clear{
		display: flex; align-items: center; justify-content: center; padding: 10px 17px; font-size: 15px; color: var(--textColor); cursor: pointer; .transition(color);
		span{
			padding-left: 24px; position: relative;
			&:before{.icon-refresh(); font: 18px/1 var(--fonti); color: var(--textColor); position: absolute; top: 1px; left: 0; .transition(color);}
		}
		@media (min-width: @t){
			&:hover{
				color: var(--black);
				span:before{color: var(--black);}
			}
		}

		@media (max-width: @m){
			min-width: 100px; height: 48px; margin-right: 12px; padding: 5px 15px; border: 1px solid var(--blueDark); border-radius: 24px; color: var(--blueDark);
			span{
				padding: 0;
				&::before{content: none;}
			}
		}
	}
	.cf-btn-confirm{
		display: none;

		@media (max-width: @m){display: inline-flex; flex-grow: 1; height: 48px;}
	}

	.cf-color-img{
		display: flex; align-items: center; justify-content: center; width: 20px; height: 20px; background: var(--white); border-radius: 3px; position: absolute; top: 0; left: 0;
		:deep(img){display: block; width: auto; height: auto; max-width: 100%; max-height: 100%; border-radius: 3px;}
	}
	.cf-item-a_barva-48, .cf-item-a_barva-pim-6698538{
		.cf-row{margin-left: 2px;}
		.cf-row input[type='checkbox']:checked + label{
			.cf-color-img:before{.pseudo(22px,22px); border: 1px solid var(--black); z-index: 1;}
		}
	}
	.cf-rate{
		display: flex; margin-right: 8px;
		:deep(.icon-star-empty){
			display: inline-block; position: relative; width: 15px; height: 15px; margin-right: 2px; opacity: 1!important;
			&:after{.pseudo(15px,15px); background: url(assets/images/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
			
		}
		:deep(.icon-star){
			width: 16px; height: 16px;
			&:after{width: 16px; height: 16px; background: url(assets/images/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
		}
	}

	//range slider
	.cf-range{display: flex; flex-flow: column; width: 100%; height: auto; position: relative;}
	:deep(.cf-range-slider){
		margin: 30px 0 16px;

		--slider-height: 5px;
		--slider-radius: 0;
		--slider-bg: #D9D9D9;
		--slider-connect-bg: var(--blueDark);
		--slider-handle-height: 20px;
		--slider-handle-width: 20px;
		--slider-handle-radius: 100%;
		--slider-handle-bg: var(--white);
		--slider-handle-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.25);
		.slider-handle-lower{right: -19px;}
		.slider-handle-upper{margin-right: 9px;}
	}
	:deep(.cf-range-input){
		display: flex; align-items: center; justify-content: space-between;
		input{flex-grow: 1; height: 50px; background: var(--gray3); border-radius: 8px; font-size: 16px;}
	}
	:deep(.cf-range-separator){flex-shrink: 0; width: 6px; height: 1px; margin: 0 10px; background: var(--black); font-size: 0; line-height: 50px;}

	.cf-btn-m{
		display: none;

		@media (max-width: @m){
			display: flex; position: fixed; bottom: 14px; right: 12px; z-index: 11;
			span{
				padding-left: 22px; position: relative;
				&:before{.icon-filter(); font: 15px/1 var(--fonti); color: var(--white); position: absolute; top: 2px; left: 0;}
			}
		}
	}
</style>