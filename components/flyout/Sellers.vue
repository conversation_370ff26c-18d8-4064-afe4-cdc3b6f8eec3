<template>
	<FlyoutLayout>
		<template #header>
			<span v-html="offersFlyoutTitle"/>
		</template>	
		<template #content>
			<template v-if="item.offers?.length">
				<div v-for="(item, index) in item.offers" :key="item.id" class="seller-item">
					<div class="seller-title">
						<input type="radio" name="offer" :id="'seller'+item.id" v-model="selectedSeller" :value="item.id" @click="selectOffer(item)" />
						<label :for="'seller'+item.id">{{ item.seller_title }}</label>
					</div>
					<div class="seller-price" v-if="item.price_custom"><BaseUtilsFormatCurrency :price="item.price_custom" /></div>

					<div class="badge" v-if="sellerBadge(item.badges?.length)">{{sellerBadge(item.badges)}}</div>
					<CatalogProductBadges :item="item" :types="['condition']" />
					
					<div v-if="item.status && item.status == 7" class="cd-seller-item-status"><BaseCmsLabel code="ni_na_zalogi" /></div>
					<div v-else-if="shippingDate" class="cd-seller-item-shipping">
						<span v-if="new Date(offerShippingDate[index] * 1000).toDateString() === new Date().toDateString()" v-html="labels.get('seller_item_time_of_delivery_today').replace('%s%', shippingDate[index])"></span>
						<span v-else-if="new Date(offerShippingDate[index] * 1000).toDateString() === tomorrow.toDateString()" v-html="labels.get('seller_item_time_of_delivery_tomorow').replace('%s%', shippingDate[index])"></span>
						<span v-else v-html="labels.get('seller_item_time_of_delivery').replace('%s%', shippingDate[index])"></span>
						<template v-if="item.shipping_options?.length">
							<template v-if="item.shipping_options[0].shipping_price <= 0">
								- <strong class="green"><BaseCmsLabel code="free" /></strong>
							</template>
						</template>
					</div>

					<div class="seller-link">
						<NuxtLink :to="item.seller_url_product_page_without_domain ? item.seller_url_product_page_without_domain : item.seller_url_without_domain" @click="modal.close()"><BaseCmsLabel code="seller_details" /></NuxtLink>
					</div>
				</div>
			</template>

			<div class="sellers-info cms-content">
				<div class="sellers-info-title"><BaseCmsLabel code="shop_extra_info_flyout_title_pdp" /></div>
				<BaseCmsLabel code="shop_extra_info_flyout_content_pdp" tag="div" class="cnt" />
			</div>
		</template>
		<template #footer>
			<button class="btn btn-outline btn-icon btn-icon-eye btn-view" :class="{'loading': loading}" @click="modal.close(); scrollTo('.cd-main', {offset: 100});">
				<UiLoader class="dots dark" v-if="loading" />
				<BaseCmsLabel v-else code="view_product" />
			</button>
			<BaseWebshopAddToCart v-slot="{onAddToCart, loading}" v-if="item.is_available" :data="{modalData: item, shopping_cart_code: item.shopping_cart_code, quantity: 1}" @addedToCart="modal.close()">
				<button class="btn cd-btn-add" @click="onAddToCart();" :disabled="loading">
					<UiLoader v-if="loading" mode="dots" />
					<BaseCmsLabel v-else code='cd_add_to_shopping_cart' tag="span" />
				</button>
			</BaseWebshopAddToCart>
		</template>
	</FlyoutLayout>
</template>

<script setup>
	const item = useState('product');
	const labels = useLabels();
	const modal = useModal();
	const endpoints = useEndpoints();
	const config = useAppConfig();
	const content = computed(() => modal.get('flyout')?.content || null);
	const loading = ref(false);
	const {scrollTo} = useDom();
	const selectedSeller = ref(item.value.offer_id || null);

	const offersFlyoutTitle = computed(() => {
		let text = labels.get('offers_descl');
		if (item?.value?.offers_other_total === 1) {
			text = labels.get('offers_1_descl');
		} else if (item?.value?.offers_other_total === 2) {
			text = labels.get('offers_2_descl');
		} else if (item?.value?.offers_other_total === 3 || item?.value?.offers_other_total === 4) {
			text = labels.get('offers_3_descl');
		}

		return labels
			.get('seller_flyout_title')
			.replace('%s%', item?.value?.offers_other_total ?? 0)
			.replace('%offers_descl%', text);
	});

	// seller UAU badges https://markerdoo.eu.teamwork.com/app/tasks/26431834
	function sellerBadge(badges) {
		if(!badges?.length) return null;

		let badgesToShow = ['1220795', '1220796', '1220797', '1220798', '1220799', '1220800', '1567938', '1567940', '1567943', '1567946', '1567947']; // prod
		if(['mp', 'local'].includes(config.default)) {
			badgesToShow = ['2232753', '2232754', '2232755', '2232756', '2232757', '2232758']; // dev
		}

		const badgeItems = badges.filter(b => badgesToShow.includes(b.id));
		return badgeItems?.length ? badgeItems[0].title : null;
	}

	//seller shipping date
	const tomorrow = new Date();
	tomorrow.setDate(tomorrow.getDate() + 1);
	const offerShippingDate = computed(() => {
		return item.value.offers.map((item) => {
			let offerShipping = item?.shipping_options?.find(option => option.id === item?.shipping_type) || null;
			return item.status == '5' ? item?.date_available : offerShipping?.min_delivery_date;
		});
	});
	const shippingDate = computed(() => {
		const calendarMonth = ['januarja', 'februarja', 'marca', 'aprila', 'maja', 'junija', 'julija', 'avgusta', 'septembra', 'oktobra', 'novembra', 'decembra'];
		const calendarDays = ['nedelje', 'ponedeljka', 'torka', 'srede', 'četrtka', 'petka', 'sobote'];

		return item.value.offers.map((item) => {
			let offerShipping = item?.shipping_options?.find(option => option.id === item?.shipping_type) || null;
			let offerShippingDate =  item.status == '5' ? item?.date_available : offerShipping?.min_delivery_date;
			if (offerShippingDate) {
				const shippingDateDay = new Date(offerShippingDate * 1000).getDay();
				const shippingDateMonth = new Date(offerShippingDate * 1000).getMonth();

				if (calendarDays[shippingDateDay] && calendarMonth[shippingDateMonth]) {
					if (new Date(offerShippingDate * 1000).toDateString() === new Date().toDateString() || new Date(offerShippingDate * 1000).toDateString() === new Date(Date.now() + 86400000).toDateString()) {
						return new Date(offerShippingDate * 1000).getDate() + '.' + (new Date(offerShippingDate * 1000).getMonth() + 1) + '.';
					} else {
						return calendarDays[shippingDateDay] + ', ' + new Date(offerShippingDate * 1000).getDate() + '.' + (new Date(offerShippingDate * 1000).getMonth() + 1) + '.';
					}
				} else {
				return new Date(offerShippingDate * 1000).toLocaleString('en-US', {weekday: 'short', day: 'numeric', month: 'long'});
				}
			} else {
				return '';
			}
		});
	});

	// Get data for selected offer and update main product
	async function selectOffer(offer) {
		if(!offer.shopping_cart_code) {
			return console.error('Missing offer data');
		}
		loading.value = true;

		const ep = endpoints.get('_get_hapi_catalog_offer').replace('%CODE%', offer.shopping_cart_code);
		const res = await useApi(`${ep}?check_lists=${config.catalog.fetch.check_lists}`);
		if(res.data?.id) {
			item.value = res.data;
		} else {
			return navigateTo('/404/');
		}

		// FIXME fale thumbs podaci kod sellera
		loading.value = false;
	}
</script>

<style scoped lang="less">
.seller-item{
	border-bottom: 1px solid var(--gray3); padding: 25px 0; position: relative; padding: 24px var(--flyoutSideOffset);
	&.active{
		.seller-title:before{background: var(--blueDark); box-shadow: inset 0px 0px 0px 3px #fff;}
	}
}
.seller-title{font-weight: bold; font-size: 16px; padding: 0 0 12px; position: relative;}
.seller-price{position: absolute; top: 25px; right: var(--flyoutSideOffset); font-weight: bold; font-size: 16px;}
.sellers-info{padding: 24px var(--flyoutSideOffset);}
.sellers-info-title{font-size: 16px; font-weight: bold; padding: 0 0 15px;}
.seller-link{
	padding: 10px 0 0;
	a{color: var(--textColor);}
}
.btn-view.loading{
	background: none; pointer-events: none;
	&:before{display: none;}
}
:deep(.badges){margin-bottom: 10px;}
</style>