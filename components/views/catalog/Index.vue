<template>
	<BaseCatalogCategory v-slot="{item: category, contentType}" :seo="true">
		<div class="c-main">
			<LazyCatalogCategoryPromo v-if="contentType == 'category'" :category="category" />
			<BaseCatalogItemsLayout v-slot="{itemsLayout}" default="grid">
				<div class="wrapper">
					<BaseCatalogProducts v-slot="{items: products, loading, pagination, nextPage}" :infinite-scroll="0" thumb-preset="catalogEntry">
						<!-- catalog header -->
						<div class="c-header">
							<CmsBreadcrumbs v-if="category?.breadcrumbs" :items="category.breadcrumbs" class="c-bc" />
							
							<CatalogSellerHeader v-if="contentType == 'seller'" :item="category" :hide-products-url="true" />	
							<template v-else>
								<h1 class="c-title" v-if="category?.seo_h1">
									<span v-html="category.seo_h1"></span>
									<span v-if="pagination.items?.total > 0" class="c-title-counter" data-nosnippet>{{ pagination.items.total }}</span>
								</h1>
								<BaseUiToggleContent 
									:content="category.content" 
									:limit="300" 
									:append="{
										show: {
											title: labels.get('category_description_more')
										},
										hide: {
											title: labels.get('category_description_less')
										}
									}" 
									v-slot="{content}">
									<div v-if="content" class="cms-content c-desc-content" v-html="content" v-interpolation />
								</BaseUiToggleContent>
							</template>
						</div>

						<!-- top products -->
						<CatalogCategoryTopProducts v-if="category?.id && contentType == 'category'" :category="category" />
						
						<!-- toolbar -->
						<ClientOnly>
							<div class="c-toolbar" id="cToolbar">
								<LazyCatalogCategoriesCardFilters v-if="contentType == 'list' || contentType == 'seller'" />
								<BaseCatalogCategoriesWidget
									v-if="contentType == 'category'"
									:fetch="{start_position: category?.position_h, level_to: +category.level + 1, response_fields: ['id','level','url_without_domain', 'title']}"
									v-slot="{items: categories}">
									<div class="c-categories" v-if="categories?.length">
										<NuxtLink v-for="category in categories" :key="category.id" :to="category?.url_without_domain" class="c-category" >{{ category.title }}</NuxtLink>
									</div>
								</BaseCatalogCategoriesWidget>
								<LazyCatalogSort v-if="pagination.items?.total > 1" />
							</div>
						</ClientOnly>

						<!-- filters -->
						<CatalogFilters />

						<div class="c-items-wrapper">
							<!-- items -->
							<template  v-if="products?.length">
								<div class="c-items" :class="[itemsLayout, {'items-loading': loading}]">
									<BaseCatalogPromo v-slot="{items: promoItems}">
										<template v-for="(product, index) in products" :key="product.id">
											<template v-if="itemsLayout == 'list'">
												<CatalogIndexEntryList :item="product" />
											</template>

											<template v-if="itemsLayout == 'grid'">
												<template v-if="index == 4 && promoItems?.length">
													<CatalogIndexEntryPromo :item="promoItems[0]" />
												</template>
												<CatalogIndexEntry :item="product" />
											</template>
										</template>
									</BaseCatalogPromo>
								</div>
								<UiPagination class="c-pagination-items" scrollToElement="cToolbar" />
							</template>
							<template v-else>
								<div><BaseCmsLabel code="no_products" /></div>
							</template>
						</div>
					</BaseCatalogProducts>
				</div>
			</BaseCatalogItemsLayout>
		</div>
	</BaseCatalogCategory>

	<ClientOnly>
		<Teleport to="body">
			<CatalogCompare />
		</Teleport>
	</ClientOnly>
</template>

<script setup>
	const labels = useLabels();
</script>

<style lang="less" scoped>
	:deep(.swiper-button-prev), :deep(.swiper-button-next){
		display: flex; align-items: center; justify-content: center; width: 48px; height: 48px; background: rgba(210, 210, 215, 0.7); border-radius: 100%; font-size: 0; line-height: 0; position: absolute; left: 19px; top: 50%; transform: translateY(-50%); z-index: 1; cursor: pointer; .transition(background);
		&:before{.icon-arrow-down(); margin-right: 4px; font: 14px/1 var(--fonti); color: var(--black); position: absolute; z-index: 1; .rotate(90deg); .transition(color);}
		@media (min-width: @t){
			&:hover{background: rgba(154, 154, 154, 0.8);}
		}
	}
	:deep(.swiper-button-next){
		left: unset; right: 19px; transform: translateY(-50%);
		&:before{margin: 0 0 0 4px; .rotate(-90deg);}
	}
	:deep(.swiper-button-disabled){opacity: 0;}
	:deep(.swiper-button-lock){display: none;}

	.c-main{position: relative;}
	.c-bc{
		@media (max-width: @m){display: none;}
	}

	//catalog header
	.c-header{
		margin: 0 0 32px;
		@media (max-width: @m){margin: 16px 12px 26px; text-align: center;}
	}
	.c-title{
		display: inline-flex; align-items: center; padding: 0; font-size: 24px; line-height: 1.3; font-weight: 600; padding-bottom: 5px;
		@media (max-width: @m){font-size: 16px;}
	}
	.c-title-counter{
		display: flex; align-items: center; justify-content: center; margin-left: 11px; padding: 2px 11px; background: #E3E3E4; border-radius: 58px; font-size: 13px; font-weight: 500; letter-spacing: -0.25px; color: #696F71;
		@media (max-width: @m){display: none;}
	}
	.c-desc-content{
		:deep(h3), :deep(h4){padding: 5px 0 10px;}
	}
	:deep(.btn-toggle-content) {
		text-decoration: underline; cursor: pointer; z-index: 1;
		@media (min-width: @t){
			&:hover{text-decoration: underline;}
		}
	}

	//toolbar
	.c-toolbar{
		display: flex; align-items: flex-start; margin-bottom: 28px; padding-bottom: 25px; border-bottom: 1px solid var(--gray2);
		&:empty{display: none;}
		@media (max-width: @m){margin-bottom: 12px; padding-bottom: 0; border: none;}
	}
	.c-categories{
		display: flex; flex-wrap: wrap; gap: 12px; margin-right: 40px;
		@media (max-width: @m) {
			display: flex; flex-wrap: nowrap; width: calc(~'100% - -24px'); gap: unset; margin: 0 -12px; padding: 0 12px; overflow-y: hidden; white-space: normal; position: relative;
			&::-webkit-scrollbar{display: none;}
			&::-webkit-scrollbar-thumb{display: none;}
		}
	}
	.c-category{
		display: flex; align-items: center; justify-content: center; min-height: 42px; padding: 0 20px; background: var(--blueDark); border-radius: 8px; font-size: 15px; line-height: 1.35; text-decoration: none; color: var(--white); .transition(all);
		@media (min-width: @t){
			&:hover{background: var(--turquoise); color: var(--blueDark);}
		}
		@media (max-width: @m){
			margin-right: 8px; font-size: 14px; white-space: nowrap;
			&:last-child{margin-right: 0;}
		}
	}

	//items
	.c-items-wrapper{
		margin-bottom: 64px;

		@media (max-width: @m){margin-bottom: 28px;}
	}

	// top products
	:deep(.cw-double){margin: 0 0 70px;}
	:deep(.section-title){padding: 0; font-size: 23px;}
</style>
