<template>
	<BaseWebshopAddToCartModal v-slot="{items, status, onClose, urls}" :auto-close="0">
		<div class="flyout" v-if="items?.length">
			<div class="flyout-body">
				<div class="close" @click="onClose">X</div>
				<div class="header">
					<BaseCmsLabel :code='"modal_" + status?.data?.label_name' />
				</div>
				<div class="content">
					<div class="item-wrapper" v-for="(item, index) in items" :key="item.id">
						<!-- product -->
						<div class="item">
							<div class="item-seller" v-if="item.seller_title">{{index + 1}}. <BaseCmsLabel code="seller" />: {{item.seller_title}}</div>
							<div class="item-body">
								<div class="item-image">
									<BaseUiImage loading="lazy" :data="item?.images?.[0]?.file_thumbs?.['width240-height240']" default="/images/no-image.jpg" />
								</div>
								<div class="item-cnt">
									<div class="item-title">{{ item.title }}</div>
									<div class="item-footer">
										<CatalogProductPrice class="item-price" :item="item" />
										<BaseWebshopRemoveProduct :item="item" v-slot="{onRemove}">
											<div class="item-remove link" @click="onRemove(), onClose()"><BaseCmsLabel code="remove" /></div>
										</BaseWebshopRemoveProduct>
									</div>
								</div>
							</div>
						</div>

						<!-- services -->
						<div class="item-services" v-if="item.services?.length">
							<template v-for="service in item.services" :key="service.code">
								<div class="item-service" v-if="service.type == 'c' && service.items?.length">
									<div class="item-service-title">{{ service.title }}</div>
									<div class="item-service-item" v-for="serviceItem in service.items" :key="serviceItem.id">
										<div class="item-service-item-toggle" :class="{'active': serviceItem.activeDescription}" v-if="serviceItem.description" @click="serviceItem.activeDescription = !serviceItem.activeDescription">+</div>
										<input type="checkbox" :name="'service' + serviceItem.id" :value="serviceItem.id" v-model="selectedServices" :id="'service-' + serviceItem.id">
										<label :for="'service-' + serviceItem.id" @click="updateService(item)">{{ serviceItem.title }}</label>
										<div class="item-service-item-price"><BaseUtilsFormatCurrency :price="serviceItem.price" /></div>
										<div class="item-service-item-desc" v-if="serviceItem.description" v-show="serviceItem.activeDescription" v-html="serviceItem.description" />
									</div>
								</div>

								<div class="item-service" v-if="service.type == 's' && service.items?.length">
									<div class="item-service-title item-service-title-insurance">{{ service.title }}</div>
									<div class="item-service-item" v-for="serviceItem in service.items" :key="serviceItem.id">
										<div class="item-service-item-toggle" :class="{'active': serviceItem.activeDescription}" v-if="serviceItem.description" @click="serviceItem.activeDescription = !serviceItem.activeDescription">+</div>
										<input type="radio" name="insurance" :value="serviceItem.id" v-model="selectedInsurance" :id="'service-' + serviceItem.id">
										<label :for="'service-' + serviceItem.id" @click="updateService(item)">{{ serviceItem.title }}</label>
										<div class="item-service-item-price"><BaseUtilsFormatCurrency :price="serviceItem.price" /></div>
										<div class="item-service-item-desc" v-if="serviceItem.description" v-show="serviceItem.activeDescription" v-html="serviceItem.description" />
									</div>
									<div class="item-service-item">
										<input type="radio" name="insurance" value="" v-model="selectedInsurance" id="service0">
										<label for="service0" @click="updateService(item)"><BaseCmsLabel code="no_warranty" /></label>
									</div>
								</div>
							</template>
						</div>
					</div>
				</div>
				<div class="footer">
					<button class="btn-outline" @click="onClose()"><BaseCmsLabel code="continue_shopping" /></button>
					<NuxtLink class="btn" @click="onClose()" :to="urls.webshop_shopping_cart"><BaseCmsLabel code="view_shopping_cart" /></NuxtLink>
				</div>
			</div>
			<div class="flyout-mask" />
		</div>
	</BaseWebshopAddToCartModal>
</template>

<script setup>
	const webshop = useWebshop();
	const currentlySelectedInsurance = useState('productSelectedInsurance', () => "");
	const currentlySelectedServices = useState('productSelectedServices', () => []);

	const selectedServices = ref([]);
	const selectedInsurance = ref('');

	// Watch for changes in the state and set initial values if services were selected on product details page
	watch(currentlySelectedInsurance, (newValue) => {
		selectedInsurance.value = newValue || "";
	});
	watch(currentlySelectedServices, (newValue) => {
		selectedServices.value = newValue || [];
	});

	let timeout;
	async function updateService(item) {
		let data = [];
		
		if(timeout) clearTimeout(timeout);
		timeout = setTimeout(async () => {
			if(selectedInsurance.value) data.push(selectedInsurance.value);
			if(selectedServices.value?.length) selectedServices.value.forEach(el => data.push(el));
			
			await webshop.updateProduct([{
				shopping_cart_code: item.shopping_cart_code,
				quantity: 1,
				services: data.length ? data : [''],
			}]);
		}, 400);
	};
</script>

<style lang="less" scoped>
	*{
		--flyoutSideOffset: 30px;
	}
	.flyout{position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 999999; font-size: 15px;}
	.flyout-mask{position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5);}
	.flyout-body{background: #fff; width: 35%; position: fixed; top: 0; right: 0; bottom: 0; display: flex; flex-direction: column; z-index: 1;}
	.close{
		position: absolute; display: flex; align-items: center; justify-content: center; top: 30px; right: var(--flyoutSideOffset); width: 25px; height: 25px; font-size: 0; cursor: pointer;
		&:before{.icon-x(); font: 20px/1 var(--fonti);}
	}

	.header{
		font-size: 24px; padding: 25px calc(var(--flyoutSideOffset) + 50px) 20px var(--flyoutSideOffset); font-weight: bold; display: flex; gap: 10px; border-bottom: 1px solid var(--gray3); align-items: flex-start;
		&:before{.icon-check(); color: #fff; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; background: var(--green); border-radius: 100px; font: 19px/1 var(--fonti); top: 5px; position: relative;}
	}
	.content{overflow: auto; flex-grow: 1; overscroll-behavior: contain;}
	.footer{
		background: #fff; padding: 20px var(--flyoutSideOffset); display: flex; gap: 10px; margin-top: auto; border-top: 1px solid var(--gray3);
	}
	button, .btn{width: 50%; height: 60px; font-size: 18px;}

	// product item
	.item{padding: var(--flyoutSideOffset);}
	.item-body{display: flex; gap: 25px;}
	.item-title{padding: 0 0 15px; font-size: 15px;}
	.item-image{
		flex: 0 0 110px; display: flex; justify-content: center; align-items: flex-start;
		&:deep(img){max-width: 100%; height: auto; display: block;}
	}
	.item-footer{display: flex; justify-content: space-between; align-items: flex-end;}
	.item-seller{font-weight: bold; font-size: 18px; padding: 0 0 25px;}
	.item-cnt{flex-grow: 1;}
	.item-remove{color: var(--blueDark); font-size: 14px;}
	.item-price{
		padding: 0; flex-wrap: wrap;
		:deep(.current-price){width: 100%;}
	}

	// services
	.item-service{border-top: 1px solid var(--gray3); padding: 25px var(--flyoutSideOffset);}
	.item-service-title{
		font-size: 18px; font-weight: bold; position: relative; padding: 0 0 8px 28px;
		&:before{.icon-service(); font: 21px/1 var(--fonti); color: var(--blueDark); position: absolute; top: 2px; left: 0;}
		&.item-service-title-insurance:before{.icon-insurance();}
	}
	.item-service-item{
		position: relative; padding: 13px 0; border-bottom: 1px solid var(--gray3);
		label{font-weight: bold;}
		&:last-child{border-bottom: none;}
	}
	.item-service-item-price{position: absolute; top: 13px; right: 40px; font-weight: bold;}
	.item-service-item-desc{
		padding: 10px 0 0; font-size: 12px;
		:deep(p){padding: 0;}
	}
	.item-service-item-toggle{
		position: absolute; top: 11px; right: -5px; cursor: pointer; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; font-size: 0; z-index: 10;
		&:after{.icon-arrow-down(); font: 10px/1 var(--fonti);}
		&.active:after{.scaleY(-1);}
	}
</style>